'use client'
import React, { useEffect, useState } from 'react';

const Home = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredStat, setHoveredStat] = useState<number | null>(null);
  const [hoveredBenefit, setHoveredBenefit] = useState<number | null>(null);
  const [hoveredOpportunity, setHoveredOpportunity] = useState<number | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const heroImages = [
    'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=600&h=600&fit=crop&crop=face',
    // 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=600&fit=crop',
    // 'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=600&h=600&fit=crop'
  ];

  useEffect(() => {
    setIsVisible(true);

    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % heroImages.length);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const stats = [
    { label: 'Active Job Openings', value: '50+', color: 'from-slate-100 to-slate-200' },
    { label: 'Companies Partnered', value: '25+', color: 'from-gray-100 to-gray-200' },
    { label: 'Successful Placements', value: '500+', color: 'from-slate-50 to-slate-150' },
    { label: 'Training Programs', value: '15+', color: 'from-gray-50 to-gray-150' },
  ];

  const benefits = [
    {
      icon: '✅',
      title: 'Curated Opportunities',
      description: 'We collaborate with top companies like Sudha Software Solutions Private Limited and Smart Dine Menu to bring you quality listings that matter.',
      gradient: 'from-blue-50 to-blue-100'
    },
    {
      icon: '🎯',
      title: 'Comprehensive Job Details',
      description: 'Dive deep into job descriptions, company cultures, and career paths to find your perfect fit.',
      gradient: 'from-indigo-50 to-indigo-100'
    },
    {
      icon: '🚀',
      title: 'Seamless Navigation',
      description: 'Our user-friendly interface with smart filtering options lets you quickly search by location, category, or specific interests.',
      gradient: 'from-slate-50 to-slate-100'
    },
  ];

  const opportunities = [
    {
      icon: '🔥',
      title: 'Hot Jobs',
      subtitle: 'In-demand roles updated daily',
      description: 'Get instant access to the latest and most in-demand roles in the market.',
      gradient: 'from-orange-50 to-orange-100'
    },
    {
      icon: '💎',
      title: 'Exclusive Internships',
      subtitle: 'Gain hands-on experience',
      description: 'Find internships that provide hands-on experience and valuable industry exposure.',
      gradient: 'from-purple-50 to-purple-100'
    },
    {
      icon: '⚡',
      title: 'Dynamic Training Programs',
      subtitle: 'Accelerate your career',
      description: 'Enhance your skills with specialized training designed to accelerate your career.',
      gradient: 'from-yellow-50 to-yellow-100'
    },
  ];

  const FloatingElement = ({ children, delay = 0 }: { children: React.ReactNode; delay?: number }) => (
    <div
      className="animate-bounce"
      style={{
        animationDelay: `${delay}s`,
        animationDuration: '3s'
      }}
    >
      {children}
    </div>
  );

  return (
    <main className="relative min-h-screen bg-white overflow-x-hidden">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div
          className="absolute w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl transition-transform duration-1000 ease-out"
          style={{
            transform: `translate(${mousePosition.x * 0.02}px, ${mousePosition.y * 0.02}px)`
          }}
        />
        <div
          className="absolute top-1/2 right-0 w-72 h-72 bg-gradient-to-r from-pink-400/10 to-red-400/10 rounded-full blur-3xl transition-transform duration-1000 ease-out"
          style={{
            transform: `translate(${mousePosition.x * -0.01}px, ${mousePosition.y * -0.01}px)`
          }}
        />
      </div>

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 sm:py-16 md:py-20 lg:py-24 xl:py-32 lg:px-24">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiM5QzkyQUMiIGZpbGwtb3BhY2l0eT0iMC4wMyI+PGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMiIvPjwvZz48L2c+PC9zdmc+')] opacity-40 animate-pulse"></div>
        <div className="relative mx-auto max-w-none px-4 sm:px-6 md:px-8 lg:px-12 xl:px-40 2xl:px-44">
          <div className="grid items-center gap-8 sm:gap-10 lg:gap-12 lg:grid-cols-2">
            <div className={`space-y-6 sm:space-y-8 text-center lg:text-left transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-12 opacity-0'}`}>
              <FloatingElement delay={0.5}>
                <div className="inline-flex items-center rounded-full bg-gradient-to-r from-blue-100 to-purple-100 px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium text-blue-700 border border-blue-200 hover:scale-105 transition-transform cursor-pointer">
                  <span className="mr-1.5 sm:mr-2 animate-pulse">🚀</span>
                  Career Opportunities Await
                </div>
              </FloatingElement>

              <div className="space-y-4 sm:space-y-6">
                <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl xl:text-6xl font-bold tracking-tight text-gray-900 leading-tight">
                  Welcome to Our{' '}
                  <span className="bg-gradient-to-r from-red-600 via-pink-600 to-purple-600 bg-clip-text text-transparent animate-pulse">
                    Career Portal
                  </span>
                </h1>
                <p className="text-base sm:text-lg lg:text-xl leading-7 sm:leading-8 text-gray-600 max-w-2xl mx-auto lg:mx-0">
                  🌟 Unlock your potential and step into a future filled with endless opportunities.
                  Whether you are launching your career, seeking the next exciting role, or exploring
                  internships, training, and development programs, our platform is your gateway to success.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center lg:justify-start">
                <button className="group inline-flex items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 px-6 sm:px-8 py-3 sm:py-4 text-sm sm:text-base font-semibold text-white shadow-lg transition-all duration-300 hover:from-blue-700 hover:to-purple-700 hover:shadow-xl hover:scale-105 hover:-translate-y-1">
                  Start Your Journey
                  <span className="ml-2 group-hover:translate-x-1 transition-transform duration-300">→</span>
                </button>
                <button className="group inline-flex items-center justify-center rounded-lg border-2 border-gray-300 px-6 sm:px-8 py-3 sm:py-4 text-sm sm:text-base font-semibold text-gray-700 transition-all duration-300 hover:border-gray-400 hover:bg-gray-50 hover:scale-105 hover:-translate-y-1">
                  Browse Jobs
                  <span className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">👀</span>
                </button>
              </div>
            </div>

            <div className={`relative mt-8 lg:mt-0 transition-all duration-1000 delay-300 ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-12 opacity-0'}`}>
              <div className="absolute -inset-2 sm:-inset-4 rounded-2xl bg-gradient-to-r from-blue-600 to-purple-600 opacity-20 blur-lg animate-pulse"></div>
              <div className="relative aspect-square w-full max-w-md sm:max-w-lg lg:max-w-xl xl:max-w-2xl mx-auto group">
                <div className="relative w-full h-full rounded-2xl shadow-2xl overflow-hidden">
                  {heroImages.map((src, index) => (
                    <img
                      key={index}
                      src={src}
                      className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-1000 ${index === currentImageIndex ? 'opacity-100' : 'opacity-0'
                        }`}
                      alt="Career opportunities"
                    />
                  ))}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent group-hover:from-black/30 transition-all duration-300"></div>
                </div>
                <div className="absolute -bottom-4 -right-4 bg-white p-3 rounded-full shadow-lg animate-bounce">
                  <span className="text-2xl">💼</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Animated Stats Section */}
      <section className="bg-white py-12 sm:py-16 lg:px-24">
        <div className="mx-auto max-w-none px-4 sm:px-6 md:px-8 lg:px-12 xl:px-40 2xl:px-44">
          <div className="grid grid-cols-2 gap-4 sm:gap-6 md:gap-8 md:grid-cols-4">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="text-center group cursor-pointer"
                onMouseEnter={() => setHoveredStat(index)}
                onMouseLeave={() => setHoveredStat(null)}
              >
                <div className={`relative p-6 rounded-xl transition-all duration-700 ease-in-out ${hoveredStat === index
                  ? 'bg-gradient-to-r ' + stat.color + ' shadow-lg scale-105 -translate-y-1'
                  : 'hover:bg-gray-50 hover:shadow-md'
                  }`}>
                  <div className={`text-2xl sm:text-3xl lg:text-4xl font-bold transition-all duration-500 ease-in-out ${hoveredStat === index ? 'text-gray-800 scale-110' : 'text-blue-600'
                    }`}>
                    {stat.value}
                  </div>
                  <div className={`mt-1 sm:mt-2 text-xs sm:text-sm font-medium leading-tight transition-all duration-500 ease-in-out ${hoveredStat === index ? 'text-gray-700' : 'text-gray-600'
                    }`}>
                    {stat.label}
                  </div>
                  {hoveredStat === index && (
                    <div className="absolute inset-0 rounded-xl bg-white/10 animate-pulse"></div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Interactive Benefits Section */}
      <section className="bg-gray-50 py-12 sm:py-16 lg:py-20 xl:py-32 lg:px-24">
        <div className="mx-auto max-w-none px-4 sm:px-6 md:px-8 lg:px-12 xl:px-40 2xl:px-44">
          <div className="grid items-center gap-8 sm:gap-10 lg:gap-12 lg:grid-cols-2">
            <div className="space-y-6 sm:space-y-8 text-center lg:text-left">
              <div className="flex items-center gap-3 sm:gap-4 justify-center lg:justify-start">
                <FloatingElement delay={0.2}>
                  <div className="rounded-full bg-yellow-100 p-2 sm:p-3 hover:scale-110 transition-transform cursor-pointer">
                    <div className="h-6 w-6 sm:h-8 sm:w-8 bg-yellow-500 rounded-full flex items-center justify-center text-white text-sm sm:text-lg">
                      🎯
                    </div>
                  </div>
                </FloatingElement>
                <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight text-gray-900">
                  Why Choose{' '}
                  <span className="bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
                    Us
                  </span>
                  ? 🏆
                </h2>
              </div>

              <div className="space-y-4 sm:space-y-6">
                {benefits.map((benefit, index) => (
                  <div
                    key={index}
                    className="group cursor-pointer"
                    onMouseEnter={() => setHoveredBenefit(index)}
                    onMouseLeave={() => setHoveredBenefit(null)}
                  >
                    <div className={`rounded-lg bg-white p-4 sm:p-6 shadow-sm transition-all duration-700 ease-in-out ${hoveredBenefit === index
                      ? 'shadow-lg scale-102 -translate-y-1 bg-gradient-to-r ' + benefit.gradient
                      : 'hover:shadow-md hover:scale-101'
                      }`}>
                      <div className="flex items-start gap-3 sm:gap-4 text-left">
                        <span className={`text-xl sm:text-2xl flex-shrink-0 transition-all duration-600 ease-in-out ${hoveredBenefit === index ? 'scale-125' : ''
                          }`}>
                          {benefit.icon}
                        </span>
                        <div className="min-w-0">
                          <h3 className={`font-semibold text-sm sm:text-base transition-colors duration-500 ease-in-out ${hoveredBenefit === index ? 'text-gray-800' : 'text-gray-900'
                            }`}>
                            {benefit.title}
                          </h3>
                          <p className={`mt-1 sm:mt-2 text-xs sm:text-sm lg:text-base leading-relaxed transition-colors duration-500 ease-in-out ${hoveredBenefit === index ? 'text-gray-700' : 'text-gray-600'
                            }`}>
                            {benefit.description}
                          </p>
                        </div>
                      </div>
                      {hoveredBenefit === index && (
                        <div className="absolute inset-0 rounded-lg bg-white/10 animate-pulse pointer-events-none"></div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="relative">
              <div className="absolute -inset-2 sm:-inset-4 rounded-2xl bg-gradient-to-r from-yellow-400 to-orange-400 opacity-20 blur-lg animate-pulse"></div>
              <div className="relative aspect-square w-full max-w-md sm:max-w-lg lg:max-w-xl xl:max-w-2xl mx-auto group">
                <img
                  src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=600&h=600&fit=crop"
                  className="w-full h-full rounded-2xl shadow-xl object-cover transition-transform duration-500 group-hover:scale-105"
                  alt="Why choose us"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl group-hover:from-black/30 transition-all duration-300"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Opportunities Section */}
      <section className="py-12 sm:py-16 lg:py-20 xl:py-32 lg:px-24">
        <div className="mx-auto max-w-none px-4 sm:px-6 md:px-8 lg:px-12 xl:px-40 2xl:px-44">
          <div className="grid items-center gap-8 sm:gap-10 lg:gap-12 lg:grid-cols-2">
            <div className="relative order-2 lg:order-1">
              <div className="absolute -inset-2 sm:-inset-4 rounded-2xl bg-gradient-to-r from-green-400 to-blue-400 opacity-20 blur-lg animate-pulse"></div>
              <div className="relative aspect-square w-full max-w-md sm:max-w-lg lg:max-w-xl xl:max-w-2xl mx-auto group">
                <img
                  src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=600&h=600&fit=crop"
                  className="w-full h-full rounded-2xl shadow-xl object-cover transition-transform duration-500 group-hover:scale-105 group-hover:rotate-1"
                  alt="Featured opportunities"
                />
                <div className="absolute top-4 right-4 bg-white p-2 rounded-full shadow-lg animate-bounce">
                  <span className="text-lg">🌟</span>
                </div>
              </div>
            </div>

            <div className="order-1 space-y-6 sm:space-y-8 lg:order-2 text-center lg:text-left">
              <div className="flex items-center gap-3 sm:gap-4 justify-center lg:justify-start">
                <FloatingElement delay={0.3}>
                  <div className="rounded-full bg-green-100 p-2 sm:p-3 hover:scale-110 transition-transform cursor-pointer">
                    <div className="h-6 w-6 sm:h-8 sm:w-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm sm:text-lg">
                      🚀
                    </div>
                  </div>
                </FloatingElement>
                <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight text-gray-900">
                  🌈 Featured{' '}
                  <span className="bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
                    Opportunities
                  </span>
                </h2>
              </div>

              <div className="space-y-4 sm:space-y-6">
                {opportunities.map((opportunity, index) => (
                  <div
                    key={index}
                    className="group cursor-pointer"
                    onMouseEnter={() => setHoveredOpportunity(index)}
                    onMouseLeave={() => setHoveredOpportunity(null)}
                  >
                    <div className={`rounded-lg bg-white border border-gray-200 p-4 sm:p-6 shadow-sm transition-all duration-700 ease-in-out ${hoveredOpportunity === index
                      ? 'shadow-lg scale-102 -translate-y-1 bg-gradient-to-r ' + opportunity.gradient + ' border-transparent'
                      : 'hover:shadow-md hover:scale-101'
                      }`}>
                      <div className="flex items-start gap-3 sm:gap-4 text-left">
                        <span className={`text-xl sm:text-2xl flex-shrink-0 transition-all duration-600 ease-in-out ${hoveredOpportunity === index ? 'scale-125' : ''
                          }`}>
                          {opportunity.icon}
                        </span>
                        <div className="min-w-0">
                          <h3 className={`font-semibold text-sm sm:text-base transition-colors duration-500 ease-in-out ${hoveredOpportunity === index ? 'text-gray-800' : 'text-gray-900'
                            }`}>
                            {opportunity.title}: {opportunity.subtitle}
                          </h3>
                          <p className={`mt-1 sm:mt-2 text-xs sm:text-sm lg:text-base leading-relaxed transition-colors duration-500 ease-in-out ${hoveredOpportunity === index ? 'text-gray-700' : 'text-gray-600'
                            }`}>
                            {opportunity.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Animated CTA Section */}
      <section className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 py-12 sm:py-16 lg:py-20 relative overflow-hidden lg:px-32">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-white/10 rounded-full animate-ping"></div>
          <div className="absolute bottom-1/4 right-1/4 w-24 h-24 bg-white/10 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
        </div>

        <div className="relative mx-auto max-w-none px-4 sm:px-6 md:px-8 lg:px-12 xl:px-40 2xl:px-44 text-center">
          <div className="space-y-6 sm:space-y-8">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white animate-pulse">
              Ready to Transform Your Career?
            </h2>
            <div className="space-y-3 sm:space-y-4 text-sm sm:text-lg text-blue-100">
              <p>
                💥 Don't wait for the perfect moment—create it! Register now, explore our extensive
                listings, and take the first step towards transforming your ambition into achievement.
              </p>
              <p>
                Embrace the future with us, and let your career soar to new heights!
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
              <button className="group inline-flex items-center justify-center rounded-lg bg-white px-6 sm:px-8 py-3 sm:py-4 text-sm sm:text-base font-semibold text-blue-600 shadow-lg transition-all duration-300 hover:bg-gray-50 hover:shadow-xl hover:scale-105 hover:-translate-y-1">
                🚀 Start Your Journey Today
                <span className="ml-2 group-hover:animate-bounce">✨</span>
              </button>
              <button className="group inline-flex items-center justify-center rounded-lg border-2 border-white px-6 sm:px-8 py-3 sm:py-4 text-sm sm:text-base font-semibold text-white transition-all duration-300 hover:bg-white hover:text-blue-600 hover:scale-105 hover:-translate-y-1">
                Explore Opportunities
                <span className="ml-2 group-hover:translate-x-1 transition-transform duration-300">→</span>
              </button>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default Home;
